# PMO Template API Documentation

This document describes the CRUD operations available for PMO Template Documents and Checklist Items.

## PMO Tab Keys

The following tab keys are supported:
- `INFO` - Information tab
- `CONFIDENTIAL` - Confidential tab
- `SALES` - Sales tab
- `PRESALES` - Presales tab
- `BIDDING` - Bidding tab
- `PMO` - PMO tab
- `BIZCO` - Business Coordination tab

## PMO Template Documents

### 1. Get Documents (Pagination)

**GET** `/pmo-template/documents`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `q` (optional): Search term (searches in document name)
- `tab_key` (optional): Filter by tab key
- `order_by` (optional): Sort order (default: "tab_key ASC, name ASC, created_at DESC")

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "tab_key": "INFO",
      "name": "Document Name",
      "sharepoint_url": "https://sharepoint.example.com/document",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "created_by_id": "uuid",
      "updated_by_id": "uuid"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "total_pages": 10
  }
}
```

### 2. Get Document by ID

**GET** `/pmo-template/documents/:id`

**Response:**
```json
{
  "id": "uuid",
  "tab_key": "INFO",
  "name": "Document Name",
  "sharepoint_url": "https://sharepoint.example.com/document",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z",
  "created_by_id": "uuid",
  "updated_by_id": "uuid"
}
```

### 3. Create Document

**POST** `/pmo-template/documents`

**Request Body:**
```json
{
  "tab_key": "INFO",
  "name": "Document Name",
  "sharepoint_url": "https://sharepoint.example.com/document"
}
```

**Validation Rules:**
- `tab_key`: Required, must be one of the valid PMO tab keys
- `name`: Required, string
- `sharepoint_url`: Required, must be a valid URL

**Response:** Same as Get Document by ID (201 Created)

### 4. Update Document

**PUT** `/pmo-template/documents/:id`

**Request Body:**
```json
{
  "tab_key": "SALES",
  "name": "Updated Document Name",
  "sharepoint_url": "https://sharepoint.example.com/updated-document"
}
```

**Validation Rules:**
- `tab_key`: Optional, must be one of the valid PMO tab keys if provided
- `name`: Optional, string
- `sharepoint_url`: Optional, must be a valid URL if provided

**Response:** Same as Get Document by ID (200 OK)

### 5. Delete Document

**DELETE** `/pmo-template/documents/:id`

**Response:** 204 No Content

## PMO Template Checklist Items

### 1. Get Checklist Items (Pagination)

**GET** `/pmo-template/checklist-items`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `q` (optional): Search term (searches in item detail)
- `tab_key` (optional): Filter by tab key
- `order_by` (optional): Sort order (default: "tab_key ASC, created_at DESC")

**Response:**
```json
{
  "data": [
    {
      "id": "uuid",
      "tab_key": "INFO",
      "detail": "Checklist item detail",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z",
      "created_by_id": "uuid",
      "updated_by_id": "uuid"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "total_pages": 10
  }
}
```

### 2. Get Checklist Item by ID

**GET** `/pmo-template/checklist-items/:id`

**Response:**
```json
{
  "id": "uuid",
  "tab_key": "INFO",
  "detail": "Checklist item detail",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z",
  "created_by_id": "uuid",
  "updated_by_id": "uuid"
}
```

### 3. Create Checklist Item

**POST** `/pmo-template/checklist-items`

**Request Body:**
```json
{
  "tab_key": "INFO",
  "detail": "Checklist item detail"
}
```

**Validation Rules:**
- `tab_key`: Required, must be one of the valid PMO tab keys
- `detail`: Required, string

**Response:** Same as Get Checklist Item by ID (201 Created)

### 4. Update Checklist Item

**PUT** `/pmo-template/checklist-items/:id`

**Request Body:**
```json
{
  "tab_key": "SALES",
  "detail": "Updated checklist item detail"
}
```

**Validation Rules:**
- `tab_key`: Optional, must be one of the valid PMO tab keys if provided
- `detail`: Optional, string

**Response:** Same as Get Checklist Item by ID (200 OK)

### 5. Delete Checklist Item

**DELETE** `/pmo-template/checklist-items/:id`

**Response:** 204 No Content

## Authentication

All endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Error Responses

All endpoints may return the following error responses:

- `400 Bad Request`: Invalid request parameters or validation errors
- `401 Unauthorized`: Missing or invalid authentication token
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

**Error Response Format:**
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Error message",
    "details": {}
  }
}
```
