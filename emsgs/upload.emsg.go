package emsgs

import (
	"net/http"

	core "gitlab.finema.co/finema/idin-core"
)

var (
	UploadS3ConnectError = core.Error{
		Status:  http.StatusInternalServerError,
		Code:    "UPLOAD_S3_CONNECT_ERROR",
		Message: "Cannot connect to S3 storage",
	}

	UploadCannotUploadFile = core.Error{
		Status:  http.StatusInternalServerError,
		Code:    "UPLOAD_CANNOT_UPLOAD_FILE",
		Message: "Cannot upload file to storage",
	}

	UploadExtensionNotAllowed = core.Error{
		Status:  http.StatusBadRequest,
		Code:    "UPLOAD_EXTENSION_NOT_ALLOWED",
		Message: "File extension is not allowed",
	}

	UploadInvalidFileStream = core.Error{Status: 400, Code: "INVALID_FILE_STREAM", Message: "invalid file stream"}

	UploadFileNotFound = core.Error{
		Status:  http.StatusNotFound,
		Code:    "UPLOAD_FILE_NOT_FOUND",
		Message: "File not found in storage",
	}

	UploadFileMetaNotFound = core.Error{
		Status:  http.StatusNotFound,
		Code:    "UPLOAD_FILE_META_NOT_FOUND",
		Message: "File metadata not found",
	}
)
