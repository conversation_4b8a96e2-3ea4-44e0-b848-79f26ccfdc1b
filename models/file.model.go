package models

type FileAppKey string

const (
	FileAppKeyPMO       FileAppKey = "PMO"
	FileAppKeyClockin   FileAppKey = "CLOCKIN"
	FileAppKeyTimesheet FileAppKey = "TIMESHEET"
	FileAppKeyCommon    FileAppKey = "COMMON"
)

type File struct {
	BaseModelHardDelete
	Name     string     `json:"name" gorm:"column:name"`
	Path     string     `json:"path" gorm:"column:path"`
	URL      string     `json:"url" gorm:"column:url"`
	Size     int64      `json:"size" gorm:"column:size"`
	Type     string     `json:"type" gorm:"column:type"`
	Checksum string     `json:"checksum" gorm:"column:checksum"`
	App      FileAppKey `json:"app" gorm:"column:app"`

	// Relations
	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
}

func (File) TableName() string {
	return "files"
}
