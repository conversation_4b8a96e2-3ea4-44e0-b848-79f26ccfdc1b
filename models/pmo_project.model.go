package models

import "github.com/lib/pq"

type PMOProjectStatus string

const (
	PMOProjectStatusDraft    PMOProjectStatus = "DRAFT"
	PMOProjectStatusTor      PMOProjectStatus = "TOR"
	PMOProjectStatusBidding  PMOProjectStatus = "BIDDING"
	PMOProjectStatusPMO      PMOProjectStatus = "PMO"
	PMOProjectStatusWarranty PMOProjectStatus = "WARRANTY"
	PMOProjectStatusClosed   PMOProjectStatus = "CLOSED"
	PMOProjectStatusCancel   PMOProjectStatus = "CANCEL"
)

var PMOProjectStatuses = []string{
	string(PMOProjectStatusDraft),
	string(PMOProjectStatusTor),
	string(PMOProjectStatusBidding),
	string(PMOProjectStatusPMO),
	string(PMOProjectStatusWarranty),
	string(PMOProjectStatusClosed),
	string(PMOProjectStatusCancel),
}

type PMOProject struct {
	BaseModel
	Name        string           `json:"name" gorm:"column:name"`
	Slug        string           `json:"slug" gorm:"column:slug"`
	Email       string           `json:"email" gorm:"column:email"`
	Tags        pq.StringArray   `json:"tags" gorm:"column:tags;type:text[]"`
	Status      PMOProjectStatus `json:"status" gorm:"column:status"`
	ProjectID   string           `json:"project_id" gorm:"column:project_id;type:uuid"`
	CreatedByID *string          `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string          `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string          `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOProject) TableName() string {
	return "pmo_projects"
}
