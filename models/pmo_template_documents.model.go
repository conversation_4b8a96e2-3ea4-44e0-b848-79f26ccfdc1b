package models

type PMOTab<PERSON>ey string

const (
	PMOTabKeyInfo         PMOTabKey = "INFO"
	PMOTabKeyConfidential PMOTabKey = "CONFIDENTIAL"
	PMOTabKeySales        PMOTabKey = "SALES"
	PMOTabKeyPresales     PMOTabKey = "PRESALES"
	PMOTabKeyBidding      PMOTabKey = "BIDDING"
	PMOTabKeyPMO          PMOTabKey = "PMO"
	PMOTabKeyBizco        PMOTabKey = "BIZCO"
)

var PMOTabKeys = []string{
	string(PMOTabKeyInfo),
	string(PMOTabKeyConfidential),
	string(PMOTabKeySales),
	string(PMOTabKeyPresales),
	string(PMOTabKeyBidding),
	string(PMOTabKeyPMO),
	string(PMOTabKeyBizco),
}

type PMOTemplateDocument struct {
	BaseModel
	TabKey        PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	Name          string    `json:"name" gorm:"column:name"`
	SharepointURL string    `json:"sharepoint_url" gorm:"column:sharepoint_url"`
	CreatedByID   *string   `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID   *string   `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID   *string   `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`
}

func (PMOTemplateDocument) TableName() string {
	return "pmo_template_documents"
}
