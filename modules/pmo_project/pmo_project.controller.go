package pmo_project

import (
	"net/http"

	"github.com/asaskevich/govalidator"
	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectController struct {
}

func (m PMOProjectController) Pagination(c core.IHTTPContext) error {
	input := &requests.PMOProjectPaginationRequest{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoProjectSvc := services.NewPMOProjectService(c)
	res, ierr := pmoProjectSvc.Pagination(c.GetPageOptions(), &services.PMOProjectPaginationOptions{
		Status: input.Status,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m PMOProjectController) Find(c core.IHTTPContext) error {
	pmoProjectSvc := services.NewPMOProjectService(c)

	if govalidator.IsUUIDv4(c.Param("id")) {
		pmoProject, err := pmoProjectSvc.Find(c.Param("id"))
		if err != nil {
			return c.JSON(err.GetStatus(), err.JSON())
		}

		return c.JSON(http.StatusOK, pmoProject)
	} else {
		pmoProject, err := pmoProjectSvc.FindBySlug(c.Param("id"))
		if err != nil {
			return c.JSON(err.GetStatus(), err.JSON())
		}

		return c.JSON(http.StatusOK, pmoProject)
	}
}

func (m PMOProjectController) Create(c core.IHTTPContext) error {
	input := &requests.PMOProjectCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoProjectSvc := services.NewPMOProjectService(c)
	payload := &services.PMOProjectCreatePayload{}
	_ = utils.Copy(payload, input)
	pmoProject, err := pmoProjectSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, pmoProject)
}

func (m PMOProjectController) Update(c core.IHTTPContext) error {
	input := &requests.PMOProjectUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoProjectSvc := services.NewPMOProjectService(c)
	payload := &services.PMOProjectUpdatePayload{}
	_ = utils.Copy(payload, input)
	pmoProject, err := pmoProjectSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, pmoProject)
}

func (m PMOProjectController) Delete(c core.IHTTPContext) error {
	pmoProjectSvc := services.NewPMOProjectService(c)
	err := pmoProjectSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}

func (m PMOProjectController) CheckSlug(c core.IHTTPContext) error {
	input := &requests.PMOProjectCheckSlug{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoProjectSvc := services.NewPMOProjectService(c)
	available, err := pmoProjectSvc.CheckSlug(utils.ToNonPointer(input.Slug))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, core.Map{"is_available": available})
}
