package pmo_project

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewPMOProjectHTTP(e *echo.Echo) {
	pmoProject := &PMOProjectController{}

	// PMO Project routes
	e.GET("/pmo/projects", core.WithHTTPContext(pmoProject.Pagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id", core.WithHTTPContext(pmoProject.Find), middleware.AuthMiddleware())
	e.POST("/pmo/projects/check-slug", core.WithHTTPContext(pmoProject.CheckSlug), middleware.AuthMiddleware())
	e.POST("/pmo/projects", core.WithHTTPContext(pmoProject.Create), middleware.AuthMiddleware())
	e.PUT("/pmo/projects/:id", core.WithHTTPContext(pmoProject.Update), middleware.AuthMiddleware())
	e.DELETE("/pmo/projects/:id", core.WithHTTPContext(pmoProject.Delete), middleware.AuthMiddleware())
}
