-- Create<PERSON>num
CREATE TYPE "public"."PMOTabK<PERSON>" AS ENUM ('INFO', 'CONFIDENTIAL', 'SALES', 'PRESALES', 'BIDDING', 'PMO', 'BIZCO');

-- CreateTable
CREATE TABLE "public"."pmo_template_documents" (
    "id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "name" TEXT NOT NULL,
    "sharepoint_url" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_template_documents_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."pmo_template_checklist_items" (
    "id" UUID NOT NULL,
    "tab_key" "public"."PMOTabKey" NOT NULL,
    "detail" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" UUID,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "updated_by_id" UUID,
    "deleted_at" TIMESTAMP(3),
    "deleted_by_id" UUID,

    CONSTRAINT "pmo_template_checklist_items_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "pmo_template_documents_tab_key_idx" ON "public"."pmo_template_documents"("tab_key");

-- CreateIndex
CREATE INDEX "pmo_template_checklist_items_tab_key_idx" ON "public"."pmo_template_checklist_items"("tab_key");
