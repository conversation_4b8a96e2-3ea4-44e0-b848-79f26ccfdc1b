package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOTemplateChecklistItemCreate struct {
	core.BaseValidator
	TabKey *string `json:"tab_key"`
	Detail *string `json:"detail"`
}

func (r *PMOTemplateChecklistItemCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.Is<PERSON>tr<PERSON>equired(r.Tab<PERSON>, "tab_key"))
	r.Must(r.IsStrRequired(r.Detail, "detail"))

	r.Must(r.IsStrIn(r.<PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.Error()
}
