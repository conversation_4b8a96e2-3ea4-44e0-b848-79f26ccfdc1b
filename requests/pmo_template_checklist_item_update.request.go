package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type PMOTemplateChecklistItemUpdate struct {
	core.BaseValidator
	TabKey *string `json:"tab_key"`
	Detail *string `json:"detail"`
}

func (r *PMOTemplateChecklistItemUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrIn(r.Tab<PERSON>, strings.Join(models.PMOTabKeys, "|"), "tab_key"))

	return r.Error()
}
